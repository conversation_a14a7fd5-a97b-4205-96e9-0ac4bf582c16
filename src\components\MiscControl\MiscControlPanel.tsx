import React, { useState } from "react";
import {
  makeStyles,
  <PERSON>,
  Badge,
  <PERSON>b<PERSON>ist,
  Tab,
  Card,
  CardHeader,
  Button,
  Dialog,
  DialogTrigger,
  DialogSurface,
  DialogTitle,
  DialogContent,
  DialogBody,
  DialogActions,
} from "@fluentui/react-components";
import {
  Settings24Regular,
  Warning24Regular,
  Shield24Regular,
  Wrench24Regular,
  Power24Regular,
  Desktop24Regular,
  ArrowClockwise24Regular,
  UsbStick24Regular,
} from "@fluentui/react-icons";
import { useDeviceStore } from "../../stores/deviceStore";
import { useAppStore } from "../../stores/appStore";
import AdbManagementCard from "./AdbManagementCard";
import DriverManagementCard from "./DriverManagementCard";
import SystemToolsCard from "./SystemToolsCard";

const useStyles = makeStyles({
  container: {
    height: "100%",
    display: "flex",
    flexDirection: "column",
    padding: "24px",
    gap: "24px",
    backgroundColor: "var(--colorNeutralBackground2)",
  },
  header: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    paddingBottom: "16px",
    borderBottom: "1px solid var(--colorNeutralStroke2)",
  },
  headerLeft: {
    display: "flex",
    alignItems: "center",
    gap: "12px",
  },
  headerRight: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
  },
  tabContainer: {
    display: "flex",
    flexDirection: "column",
    gap: "16px",
    flex: 1,
    overflow: "hidden",
  },
  tabContent: {
    flex: 1,
    overflow: "auto",
    padding: "16px 0",
  },
  warningBanner: {
    padding: "16px",
    backgroundColor: "var(--colorPaletteYellowBackground1)",
    border: "1px solid var(--colorPaletteYellowBorder1)",
    borderRadius: "8px",
    display: "flex",
    alignItems: "center",
    gap: "12px",
    marginBottom: "16px",
  },
  warningIcon: {
    color: "var(--colorPaletteYellowForeground1)",
    fontSize: "20px",
  },
  warningText: {
    color: "var(--colorPaletteYellowForeground1)",
    fontWeight: "500",
  },
  noDevice: {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    gap: "16px",
    textAlign: "center",
    color: "var(--colorNeutralForeground2)",
  },
  placeholder: {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    gap: "16px",
    textAlign: "center",
    color: "var(--colorNeutralForeground2)",
  },
  placeholderIcon: {
    fontSize: "48px",
    color: "var(--colorNeutralForeground3)",
  },
  placeholderTitle: {
    fontSize: "18px",
    fontWeight: "600",
    color: "var(--colorNeutralForeground1)",
  },
  placeholderDescription: {
    fontSize: "14px",
    color: "var(--colorNeutralForeground2)",
    maxWidth: "400px",
    lineHeight: "1.5",
  },
});

type MiscControlView = "adb" | "driver" | "system";

const MiscControlPanel: React.FC = () => {
  const styles = useStyles();
  const { selectedDevice, devices } = useDeviceStore();
  const { setStatusBarMessage } = useAppStore();
  const [currentView, setCurrentView] = useState<MiscControlView>("adb");
  const [showWarningDialog, setShowWarningDialog] = useState(false);

  const connectedDevices = devices.filter(d => d.connected);

  const tabs = [
    {
      id: "adb" as MiscControlView,
      label: "ADB管理",
      icon: <Power24Regular />,
      description: "ADB进程管理和服务控制",
    },
    {
      id: "driver" as MiscControlView,
      label: "驱动管理",
      icon: <UsbStick24Regular />,
      description: "设备驱动安装和USB修复",
    },
    {
      id: "system" as MiscControlView,
      label: "系统工具",
      icon: <Desktop24Regular />,
      description: "系统工具和应用管理",
    },
  ];

  const showWarning = (message: string) => {
    setStatusBarMessage({
      type: "warning",
      message,
      duration: 5000,
    });
  };

  const showSuccess = (message: string) => {
    setStatusBarMessage({
      type: "success",
      message,
      duration: 3000,
    });
  };

  const showError = (message: string) => {
    setStatusBarMessage({
      type: "error",
      message,
      duration: 5000,
    });
  };

  const renderContent = () => {
    switch (currentView) {
      case "adb":
        return (
          <AdbManagementCard 
            onWarning={showWarning}
            onSuccess={showSuccess}
            onError={showError}
          />
        );
      case "driver":
        return (
          <DriverManagementCard 
            onWarning={showWarning}
            onSuccess={showSuccess}
            onError={showError}
          />
        );
      case "system":
        return (
          <SystemToolsCard 
            onWarning={showWarning}
            onSuccess={showSuccess}
            onError={showError}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <Settings24Regular />
          <Text size={500} weight="semibold">杂项控制</Text>
          <Badge appearance="filled" color="brand">
            系统工具
          </Badge>
        </div>
        
        <div className={styles.headerRight}>
          <Text size={200} style={{ color: "var(--colorNeutralForeground2)" }}>
            {connectedDevices.length} 台设备已连接
          </Text>
        </div>
      </div>

      {/* 风险警告横幅 */}
      <div className={styles.warningBanner}>
        <Warning24Regular className={styles.warningIcon} />
        <Text className={styles.warningText}>
          注意：此模块包含系统级操作，请谨慎使用。某些操作可能需要管理员权限。
        </Text>
      </div>

      <div className={styles.tabContainer}>
        <TabList
          selectedValue={currentView}
          onTabSelect={(_, data) => setCurrentView(data.value as MiscControlView)}
        >
          {tabs.map((tab) => (
            <Tab
              key={tab.id}
              value={tab.id}
              icon={tab.icon}
            >
              {tab.label}
            </Tab>
          ))}
        </TabList>

        <div className={styles.tabContent}>
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default MiscControlPanel;
