import React, { useState } from "react";
import {
  makeStyles,
  Text,
  Button,
  Card,
  CardHeader,
  CardPreview,
  CardFooter,
  Spinner,
  Dialog,
  DialogTrigger,
  DialogSurface,
  DialogTitle,
  DialogContent,
  DialogBody,
  DialogActions,
} from "@fluentui/react-components";
import {
  Power24Regular,
  ArrowClockwise24Regular,
  Stop24Regular,
  Warning24Regular,
  Dismiss24Regular,
} from "@fluentui/react-icons";
import { invoke } from "@tauri-apps/api/core";

const useStyles = makeStyles({
  container: {
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
    gap: "16px",
    padding: "16px 0",
  },
  card: {
    height: "200px",
    cursor: "pointer",
    transition: "all 0.2s ease",
    "&:hover": {
      transform: "translateY(-2px)",
      boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
    },
  },
  cardContent: {
    padding: "16px",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    gap: "12px",
    height: "100%",
  },
  icon: {
    fontSize: "32px",
    color: "var(--colorBrandForeground1)",
  },
  dangerIcon: {
    fontSize: "32px",
    color: "var(--colorPaletteRedForeground1)",
  },
  title: {
    fontSize: "16px",
    fontWeight: "600",
    textAlign: "center",
  },
  description: {
    fontSize: "14px",
    color: "var(--colorNeutralForeground2)",
    textAlign: "center",
    lineHeight: "1.4",
  },
  warningDialog: {
    maxWidth: "400px",
  },
  warningContent: {
    display: "flex",
    flexDirection: "column",
    gap: "16px",
  },
  warningIcon: {
    color: "var(--colorPaletteRedForeground1)",
    fontSize: "24px",
  },
  warningText: {
    lineHeight: "1.5",
  },
});

interface AdbManagementCardProps {
  onWarning: (message: string) => void;
  onSuccess: (message: string) => void;
  onError: (message: string) => void;
}

const AdbManagementCard: React.FC<AdbManagementCardProps> = ({
  onWarning,
  onSuccess,
  onError,
}) => {
  const styles = useStyles();
  const [isExecuting, setIsExecuting] = useState<string | null>(null);
  const [showStopDialog, setShowStopDialog] = useState(false);

  const executeCommand = async (
    commandId: string,
    command: () => Promise<any>,
    description: string,
    isRisky: boolean = false
  ) => {
    if (isRisky) {
      onWarning(`即将执行: ${description}，请确认操作`);
    }

    setIsExecuting(commandId);
    try {
      const result = await command();
      if (result.success) {
        onSuccess(`${description}成功`);
      } else {
        onError(result.error || `${description}失败`);
      }
    } catch (error) {
      onError(`${description}失败: ${error}`);
    } finally {
      setIsExecuting(null);
    }
  };

  const handleStopAdb = () => {
    setShowStopDialog(true);
  };

  const confirmStopAdb = async () => {
    setShowStopDialog(false);
    await executeCommand(
      "stop-adb",
      () => invoke("stop_adb_process"),
      "停止ADB进程",
      true
    );
  };

  const handleRestartAdb = async () => {
    await executeCommand(
      "restart-adb",
      () => invoke("restart_adb_service"),
      "重启ADB服务"
    );
  };

  const tools = [
    {
      id: "stop-adb",
      title: "停止ADB进程",
      description: "强制终止当前运行的ADB进程",
      icon: <Stop24Regular className={styles.dangerIcon} />,
      action: handleStopAdb,
      isRisky: true,
    },
    {
      id: "restart-adb",
      title: "重启ADB服务",
      description: "重新启动ADB服务以恢复连接",
      icon: <ArrowClockwise24Regular className={styles.icon} />,
      action: handleRestartAdb,
      isRisky: false,
    },
  ];

  return (
    <>
      <div className={styles.container}>
        {tools.map((tool) => (
          <Card
            key={tool.id}
            className={styles.card}
            onClick={tool.action}
          >
            <div className={styles.cardContent}>
              {isExecuting === tool.id ? (
                <Spinner size="large" />
              ) : (
                tool.icon
              )}
              <Text className={styles.title}>{tool.title}</Text>
              <Text className={styles.description}>{tool.description}</Text>
            </div>
          </Card>
        ))}
      </div>

      {/* 停止ADB确认对话框 */}
      <Dialog open={showStopDialog} onOpenChange={(_, data) => setShowStopDialog(data.open)}>
        <DialogSurface className={styles.warningDialog}>
          <DialogTitle>
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <Warning24Regular className={styles.warningIcon} />
              确认停止ADB进程
            </div>
          </DialogTitle>
          <DialogContent>
            <DialogBody>
              <div className={styles.warningContent}>
                <Text className={styles.warningText}>
                  您即将强制停止ADB进程，这将断开所有设备连接。
                </Text>
                <Text className={styles.warningText}>
                  <strong>注意：</strong>此操作可能会影响正在进行的设备操作。
                </Text>
                <Text className={styles.warningText}>
                  是否确认继续？
                </Text>
              </div>
            </DialogBody>
          </DialogContent>
          <DialogActions>
            <Button
              appearance="secondary"
              onClick={() => setShowStopDialog(false)}
            >
              取消
            </Button>
            <Button
              appearance="primary"
              onClick={confirmStopAdb}
              style={{ backgroundColor: "var(--colorPaletteRedBackground1)" }}
            >
              确认停止
            </Button>
          </DialogActions>
        </DialogSurface>
      </Dialog>
    </>
  );
};

export default AdbManagementCard;
