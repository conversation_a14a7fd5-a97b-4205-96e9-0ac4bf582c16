import React, { useState } from "react";
import {
  makeStyles,
  Text,
  Button,
  Card,
  CardHeader,
  CardPreview,
  CardFooter,
  Spinner,
  Dialog,
  DialogTrigger,
  DialogSurface,
  DialogTitle,
  DialogContent,
  DialogBody,
  DialogActions,
} from "@fluentui/react-components";
import {
  UsbStick24Regular,
  Wrench24Regular,
  Shield24Regular,
  Warning24Regular,
  Info24Regular,
} from "@fluentui/react-icons";
import { invoke } from "@tauri-apps/api/core";

const useStyles = makeStyles({
  container: {
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
    gap: "16px",
    padding: "16px 0",
  },
  card: {
    height: "200px",
    cursor: "pointer",
    transition: "all 0.2s ease",
    "&:hover": {
      transform: "translateY(-2px)",
      boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
    },
  },
  cardContent: {
    padding: "16px",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    gap: "12px",
    height: "100%",
  },
  icon: {
    fontSize: "32px",
    color: "var(--colorBrandForeground1)",
  },
  warningIcon: {
    fontSize: "32px",
    color: "var(--colorPaletteYellowForeground1)",
  },
  title: {
    fontSize: "16px",
    fontWeight: "600",
    textAlign: "center",
  },
  description: {
    fontSize: "14px",
    color: "var(--colorNeutralForeground2)",
    textAlign: "center",
    lineHeight: "1.4",
  },
  warningDialog: {
    maxWidth: "450px",
  },
  warningContent: {
    display: "flex",
    flexDirection: "column",
    gap: "16px",
  },
  warningDialogIcon: {
    color: "var(--colorPaletteYellowForeground1)",
    fontSize: "24px",
  },
  warningText: {
    lineHeight: "1.5",
  },
  infoBox: {
    padding: "12px",
    backgroundColor: "var(--colorNeutralBackground3)",
    borderRadius: "6px",
    border: "1px solid var(--colorNeutralStroke2)",
  },
});

interface DriverManagementCardProps {
  onWarning: (message: string) => void;
  onSuccess: (message: string) => void;
  onError: (message: string) => void;
}

const DriverManagementCard: React.FC<DriverManagementCardProps> = ({
  onWarning,
  onSuccess,
  onError,
}) => {
  const styles = useStyles();
  const [isExecuting, setIsExecuting] = useState<string | null>(null);
  const [showDriverDialog, setShowDriverDialog] = useState(false);
  const [showUsbDialog, setShowUsbDialog] = useState(false);

  const executeCommand = async (
    commandId: string,
    command: () => Promise<any>,
    description: string,
    isRisky: boolean = false
  ) => {
    if (isRisky) {
      onWarning(`即将执行: ${description}，请确认操作`);
    }

    setIsExecuting(commandId);
    try {
      const result = await command();
      if (result.success) {
        onSuccess(`${description}成功`);
      } else {
        onError(result.error || `${description}失败`);
      }
    } catch (error) {
      onError(`${description}失败: ${error}`);
    } finally {
      setIsExecuting(null);
    }
  };

  const handleInstallDriver = () => {
    setShowDriverDialog(true);
  };

  const confirmInstallDriver = async () => {
    setShowDriverDialog(false);
    await executeCommand(
      "install-driver",
      () => invoke("install_device_driver"),
      "安装设备驱动",
      true
    );
  };

  const handleFixUsb3 = () => {
    setShowUsbDialog(true);
  };

  const confirmFixUsb3 = async () => {
    setShowUsbDialog(false);
    await executeCommand(
      "fix-usb3",
      () => invoke("fix_usb3_connection"),
      "修复USB 3.0连接",
      true
    );
  };

  const tools = [
    {
      id: "install-driver",
      title: "安装设备驱动",
      description: "自动安装Android设备驱动程序",
      icon: <Shield24Regular className={styles.icon} />,
      action: handleInstallDriver,
      isRisky: true,
    },
    {
      id: "fix-usb3",
      title: "USB 3.0修复",
      description: "修复USB 3.0连接问题，确保设备正常识别",
      icon: <Wrench24Regular className={styles.warningIcon} />,
      action: handleFixUsb3,
      isRisky: true,
    },
  ];

  return (
    <>
      <div className={styles.container}>
        {tools.map((tool) => (
          <Card
            key={tool.id}
            className={styles.card}
            onClick={tool.action}
          >
            <div className={styles.cardContent}>
              {isExecuting === tool.id ? (
                <Spinner size="large" />
              ) : (
                tool.icon
              )}
              <Text className={styles.title}>{tool.title}</Text>
              <Text className={styles.description}>{tool.description}</Text>
            </div>
          </Card>
        ))}
      </div>

      {/* 安装驱动确认对话框 */}
      <Dialog open={showDriverDialog} onOpenChange={(_, data) => setShowDriverDialog(data.open)}>
        <DialogSurface className={styles.warningDialog}>
          <DialogTitle>
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <Warning24Regular className={styles.warningDialogIcon} />
              确认安装设备驱动
            </div>
          </DialogTitle>
          <DialogContent>
            <DialogBody>
              <div className={styles.warningContent}>
                <Text className={styles.warningText}>
                  您即将安装Android设备驱动程序。
                </Text>
                <div className={styles.infoBox}>
                  <Text style={{ fontWeight: "500", marginBottom: "8px" }}>
                    <Info24Regular style={{ marginRight: "6px" }} />
                    注意事项：
                  </Text>
                  <Text className={styles.warningText}>
                    • 此操作需要管理员权限<br/>
                    • 可能需要重启计算机<br/>
                    • 请确保驱动文件存在于resources目录
                  </Text>
                </div>
                <Text className={styles.warningText}>
                  是否确认继续？
                </Text>
              </div>
            </DialogBody>
          </DialogContent>
          <DialogActions>
            <Button
              appearance="secondary"
              onClick={() => setShowDriverDialog(false)}
            >
              取消
            </Button>
            <Button
              appearance="primary"
              onClick={confirmInstallDriver}
            >
              确认安装
            </Button>
          </DialogActions>
        </DialogSurface>
      </Dialog>

      {/* USB 3.0修复确认对话框 */}
      <Dialog open={showUsbDialog} onOpenChange={(_, data) => setShowUsbDialog(data.open)}>
        <DialogSurface className={styles.warningDialog}>
          <DialogTitle>
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <Warning24Regular className={styles.warningDialogIcon} />
              确认修复USB 3.0连接
            </div>
          </DialogTitle>
          <DialogContent>
            <DialogBody>
              <div className={styles.warningContent}>
                <Text className={styles.warningText}>
                  您即将执行USB 3.0连接修复操作。
                </Text>
                <div className={styles.infoBox}>
                  <Text style={{ fontWeight: "500", marginBottom: "8px" }}>
                    <Info24Regular style={{ marginRight: "6px" }} />
                    修复内容：
                  </Text>
                  <Text className={styles.warningText}>
                    • 重新扫描USB设备<br/>
                    • 重置USB控制器<br/>
                    • 可能会短暂断开所有USB设备
                  </Text>
                </div>
                <Text className={styles.warningText}>
                  是否确认继续？
                </Text>
              </div>
            </DialogBody>
          </DialogContent>
          <DialogActions>
            <Button
              appearance="secondary"
              onClick={() => setShowUsbDialog(false)}
            >
              取消
            </Button>
            <Button
              appearance="primary"
              onClick={confirmFixUsb3}
            >
              确认修复
            </Button>
          </DialogActions>
        </DialogSurface>
      </Dialog>
    </>
  );
};

export default DriverManagementCard;
