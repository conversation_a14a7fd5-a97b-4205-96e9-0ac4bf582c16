import React, { useState } from "react";
import {
  makeStyles,
  Text,
  Button,
  Card,
  CardHeader,
  CardPreview,
  CardFooter,
  Spinner,
  Dialog,
  DialogTrigger,
  DialogSurface,
  DialogTitle,
  DialogContent,
  DialogBody,
  DialogActions,
} from "@fluentui/react-components";
import {
  Desktop24Regular,
  ArrowClockwise24Regular,
  Settings24Regular,
  Warning24Regular,
  Info24Regular,
  Shield24Regular,
} from "@fluentui/react-icons";
import { invoke } from "@tauri-apps/api/core";

const useStyles = makeStyles({
  container: {
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
    gap: "16px",
    padding: "16px 0",
  },
  card: {
    height: "200px",
    cursor: "pointer",
    transition: "all 0.2s ease",
    "&:hover": {
      transform: "translateY(-2px)",
      boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
    },
  },
  cardContent: {
    padding: "16px",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    gap: "12px",
    height: "100%",
  },
  icon: {
    fontSize: "32px",
    color: "var(--colorBrandForeground1)",
  },
  warningIcon: {
    fontSize: "32px",
    color: "var(--colorPaletteRedForeground1)",
  },
  title: {
    fontSize: "16px",
    fontWeight: "600",
    textAlign: "center",
  },
  description: {
    fontSize: "14px",
    color: "var(--colorNeutralForeground2)",
    textAlign: "center",
    lineHeight: "1.4",
  },
  warningDialog: {
    maxWidth: "450px",
  },
  warningContent: {
    display: "flex",
    flexDirection: "column",
    gap: "16px",
  },
  warningDialogIcon: {
    color: "var(--colorPaletteRedForeground1)",
    fontSize: "24px",
  },
  warningText: {
    lineHeight: "1.5",
  },
  infoBox: {
    padding: "12px",
    backgroundColor: "var(--colorNeutralBackground3)",
    borderRadius: "6px",
    border: "1px solid var(--colorNeutralStroke2)",
  },
  permissionCard: {
    gridColumn: "1 / -1",
    height: "auto",
    minHeight: "120px",
  },
  permissionContent: {
    padding: "20px",
    display: "flex",
    alignItems: "center",
    gap: "16px",
  },
  permissionInfo: {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    gap: "8px",
  },
  permissionStatus: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
    padding: "8px 12px",
    borderRadius: "6px",
    backgroundColor: "var(--colorNeutralBackground3)",
    border: "1px solid var(--colorNeutralStroke2)",
  },
});

interface SystemToolsCardProps {
  onWarning: (message: string) => void;
  onSuccess: (message: string) => void;
  onError: (message: string) => void;
}

const SystemToolsCard: React.FC<SystemToolsCardProps> = ({
  onWarning,
  onSuccess,
  onError,
}) => {
  const styles = useStyles();
  const [isExecuting, setIsExecuting] = useState<string | null>(null);
  const [showRestartDialog, setShowRestartDialog] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState<string | null>(null);

  const executeCommand = async (
    commandId: string,
    command: () => Promise<any>,
    description: string,
    isRisky: boolean = false
  ) => {
    if (isRisky) {
      onWarning(`即将执行: ${description}，请确认操作`);
    }

    setIsExecuting(commandId);
    try {
      const result = await command();
      if (result.success) {
        onSuccess(`${description}成功`);
      } else {
        onError(result.error || `${description}失败`);
      }
    } catch (error) {
      onError(`${description}失败: ${error}`);
    } finally {
      setIsExecuting(null);
    }
  };

  const handleOpenDeviceManager = async () => {
    await executeCommand(
      "open-device-manager",
      () => invoke("open_device_manager"),
      "打开设备管理器"
    );
  };

  const handleRestartApp = () => {
    setShowRestartDialog(true);
  };

  const confirmRestartApp = async () => {
    setShowRestartDialog(false);
    await executeCommand(
      "restart-app",
      () => invoke("restart_application"),
      "重启应用",
      true
    );
  };

  const checkPermissions = async () => {
    setIsExecuting("check-permissions");
    try {
      const result = await invoke("check_system_permissions");
      if (result.success) {
        setPermissionStatus(result.output);
        onSuccess("权限检查完成");
      } else {
        onError(result.error || "权限检查失败");
      }
    } catch (error) {
      onError(`权限检查失败: ${error}`);
    } finally {
      setIsExecuting(null);
    }
  };

  const tools = [
    {
      id: "open-device-manager",
      title: "打开设备管理器",
      description: "快速启动Windows设备管理器",
      icon: <Desktop24Regular className={styles.icon} />,
      action: handleOpenDeviceManager,
      isRisky: false,
    },
    {
      id: "restart-app",
      title: "重启应用",
      description: "重新启动HOUT应用程序",
      icon: <ArrowClockwise24Regular className={styles.warningIcon} />,
      action: handleRestartApp,
      isRisky: true,
    },
  ];

  return (
    <>
      <div className={styles.container}>
        {/* 权限检查卡片 */}
        <Card className={`${styles.card} ${styles.permissionCard}`}>
          <div className={styles.permissionContent}>
            <Shield24Regular className={styles.icon} />
            <div className={styles.permissionInfo}>
              <Text className={styles.title}>系统权限检查</Text>
              <Text className={styles.description}>
                检查当前应用的系统权限状态
              </Text>
              {permissionStatus && (
                <div className={styles.permissionStatus}>
                  <Info24Regular style={{ fontSize: "16px" }} />
                  <Text size={200}>{permissionStatus}</Text>
                </div>
              )}
            </div>
            <Button
              appearance="primary"
              onClick={checkPermissions}
              disabled={isExecuting === "check-permissions"}
            >
              {isExecuting === "check-permissions" ? (
                <Spinner size="tiny" />
              ) : (
                "检查权限"
              )}
            </Button>
          </div>
        </Card>

        {/* 其他工具卡片 */}
        {tools.map((tool) => (
          <Card
            key={tool.id}
            className={styles.card}
            onClick={tool.action}
          >
            <div className={styles.cardContent}>
              {isExecuting === tool.id ? (
                <Spinner size="large" />
              ) : (
                tool.icon
              )}
              <Text className={styles.title}>{tool.title}</Text>
              <Text className={styles.description}>{tool.description}</Text>
            </div>
          </Card>
        ))}
      </div>

      {/* 重启应用确认对话框 */}
      <Dialog open={showRestartDialog} onOpenChange={(_, data) => setShowRestartDialog(data.open)}>
        <DialogSurface className={styles.warningDialog}>
          <DialogTitle>
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <Warning24Regular className={styles.warningDialogIcon} />
              确认重启应用
            </div>
          </DialogTitle>
          <DialogContent>
            <DialogBody>
              <div className={styles.warningContent}>
                <Text className={styles.warningText}>
                  您即将重启HOUT应用程序。
                </Text>
                <div className={styles.infoBox}>
                  <Text style={{ fontWeight: "500", marginBottom: "8px" }}>
                    <Info24Regular style={{ marginRight: "6px" }} />
                    注意事项：
                  </Text>
                  <Text className={styles.warningText}>
                    • 当前所有操作将被中断<br/>
                    • 未保存的设置可能丢失<br/>
                    • 设备连接将被重新建立
                  </Text>
                </div>
                <Text className={styles.warningText}>
                  是否确认重启？
                </Text>
              </div>
            </DialogBody>
          </DialogContent>
          <DialogActions>
            <Button
              appearance="secondary"
              onClick={() => setShowRestartDialog(false)}
            >
              取消
            </Button>
            <Button
              appearance="primary"
              onClick={confirmRestartApp}
              style={{ backgroundColor: "var(--colorPaletteRedBackground1)" }}
            >
              确认重启
            </Button>
          </DialogActions>
        </DialogSurface>
      </Dialog>
    </>
  );
};

export default SystemToolsCard;
