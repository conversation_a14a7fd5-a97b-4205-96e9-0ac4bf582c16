import React, { useState } from "react";
import {
  makeStyles,
  Card,
  CardHeader,
  Text,
  Badge,
  Spinner,
  tokens,
} from "@fluentui/react-components";
import {
  Settings24Regular,
  Power24Regular,
  UsbStick24Regular,
  Wrench24Regular,
  Warning24Regular,
  Desktop24Regular,
  ArrowExit24Regular,
} from "@fluentui/react-icons";
import { useDeviceStore } from "../../stores/deviceStore";
import { useDeviceService } from "../../services/deviceService";
import { useAppStore } from "../../stores/appStore";

const useStyles = makeStyles({
  card: {
    height: "200px",
    display: "flex",
    flexDirection: "column",
    border: "1px solid var(--colorNeutralStroke2)",
    borderRadius: "8px",
    boxShadow: "0 2px 8px rgba(0, 0, 0, 0.08)",
    backgroundColor: "var(--colorNeutralBackground1)",
    transition: "box-shadow 0.2s ease",
    ":hover": {
      boxShadow: "0 4px 16px rgba(0, 0, 0, 0.12)",
    },
  },
  cardHeader: {
    paddingBottom: "12px",
  },
  cardTitle: {
    fontSize: "12px", // 减少标题字体大小
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
    display: "flex",
    alignItems: "center",
    gap: "6px", // 减少间距
  },
  titleIcon: {
    color: tokens.colorBrandForeground1,
    fontSize: "16px", // 减少图标大小
  },
  cardContent: {
    flex: 1,
    display: "grid",
    gridTemplateColumns: "1fr 1fr 1fr", // 改为3列布局
    gridTemplateRows: "1fr 1fr 1fr", // 保持3行布局
    gap: "4px", // 稍微增加间距
    padding: "0 8px 8px 8px",
  },
  functionItem: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    padding: "4px 2px", // 调整内边距以适应3x3网格
    borderRadius: "6px", // 稍微增加圆角
    border: `1px solid ${tokens.colorNeutralStroke3}`,
    backgroundColor: tokens.colorNeutralBackground2,
    transition: "all 0.2s ease",
    cursor: "pointer",
    minHeight: "32px", // 增加最小高度以适应3x3网格
    textAlign: "center",
    position: "relative", // 添加相对定位以支持绝对定位的徽章
    ":hover": {
      backgroundColor: tokens.colorNeutralBackground2Hover,
      transform: "translateY(-1px)", // 添加轻微的悬停效果
      boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
    },
  },
  functionInfo: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    gap: "1px", // 减少间距
    flex: 1,
  },
  functionIcon: {
    color: tokens.colorBrandForeground1,
    fontSize: "14px", // 增大图标以适应3x3网格
    marginBottom: "2px", // 添加底部间距
  },
  functionText: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    gap: "1px", // 减少间距
    width: "100%",
  },
  functionTitle: {
    fontSize: "8px", // 稍微增大字体以适应3x3网格
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
    textAlign: "center",
    lineHeight: "1.1",
    whiteSpace: "nowrap", // 防止换行
    overflow: "hidden",
    textOverflow: "ellipsis",
    width: "100%", // 占据全宽
  },
  functionDescription: {
    display: "none", // 隐藏描述以节省空间
  },
  actionButton: {
    minWidth: "60px",
  },
  warningItem: {
    borderColor: tokens.colorPaletteYellowBorder2,
    backgroundColor: tokens.colorPaletteYellowBackground1,
    ":hover": {
      backgroundColor: tokens.colorPaletteYellowBackground2,
      borderColor: tokens.colorPaletteYellowBorder1,
    },
  },
  pendingItem: {
    borderColor: tokens.colorPaletteYellowBorder1,
    backgroundColor: tokens.colorPaletteYellowBackground2,
  },
  disabledItem: {
    opacity: 0.5,
    cursor: "not-allowed",
    backgroundColor: tokens.colorNeutralBackground3,
    borderColor: tokens.colorNeutralStroke3,
    ":hover": {
      backgroundColor: tokens.colorNeutralBackground3,
      borderColor: tokens.colorNeutralStroke3,
      transform: "none",
      boxShadow: "none",
    },
  },
});

interface MiscFunction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactElement;
  isRisky: boolean;
  isDisabled?: boolean; // 新增禁用状态
  action: () => Promise<void>;
}

const MiscellaneousCard: React.FC = () => {
  const styles = useStyles();
  const { selectedDevice } = useDeviceStore();
  const { deviceService } = useDeviceService();
  const { addNotification } = useAppStore();
  
  const [executingFunction, setExecutingFunction] = useState<string | null>(null);
  const [pendingConfirmation, setPendingConfirmation] = useState<string | null>(null);
  const [confirmationTimeout, setConfirmationTimeout] = useState<number | null>(null);

  const miscFunctions: MiscFunction[] = [
    {
      id: "stop-adb",
      title: "停止ADB进程",
      description: "终止当前运行的ADB服务进程",
      icon: <Power24Regular />,
      isRisky: true,
      action: async () => {
        const result = await deviceService.stopAdbServer();
        if (result.success) {
          addNotification({
            type: "success",
            title: "ADB进程已停止",
            message: "ADB服务进程已成功终止",
          });
        } else {
          throw new Error(result.error || "停止ADB进程失败");
        }
      },
    },
    {
      id: "install-driver",
      title: "安装设备驱动",
      description: "功能开发中，敬请期待",
      icon: <UsbStick24Regular />,
      isRisky: false,
      isDisabled: true, // 设置为禁用状态
      action: async () => {
        addNotification({
          type: "info",
          title: "功能开发中",
          message: "该功能正在开发中，敬请期待",
          duration: 2000,
        });
      },
    },
    {
      id: "usb3-fix",
      title: "USB 3.0修复",
      description: "功能开发中，敬请期待",
      icon: <Wrench24Regular />,
      isRisky: false,
      isDisabled: true, // 设置为禁用状态
      action: async () => {
        addNotification({
          type: "info",
          title: "功能开发中",
          message: "该功能正在开发中，敬请期待",
          duration: 2000,
        });
      },
    },
    {
      id: "restart-adb",
      title: "重启ADB服务",
      description: "重新启动ADB服务以解决连接问题",
      icon: <Settings24Regular />,
      isRisky: false,
      action: async () => {
        const result = await deviceService.restartAdbServer();
        if (result.success) {
          addNotification({
            type: "success",
            title: "ADB服务已重启",
            message: "ADB服务已成功重新启动",
          });
        } else {
          throw new Error(result.error || "重启ADB服务失败");
        }
      },
    },
    {
      id: "open-device-manager",
      title: "打开设备管理器",
      description: "打开系统设备管理器查看设备状态",
      icon: <Desktop24Regular />,
      isRisky: false,
      action: async () => {
        // 简化实现：提供操作指引
        addNotification({
          type: "info",
          title: "打开设备管理器",
          message: "请按 Win+R，输入 devmgmt.msc 打开设备管理器",
          duration: 5000,
        });

        // 尝试复制命令到剪贴板（如果支持）
        try {
          if (navigator.clipboard) {
            await navigator.clipboard.writeText("devmgmt.msc");
            addNotification({
              type: "success",
              title: "命令已复制",
              message: "devmgmt.msc 已复制到剪贴板",
              duration: 3000,
            });
          }
        } catch (_clipboardError) {
          // 忽略剪贴板错误
        }
      },
    },
    {
      id: "restart-app",
      title: "重启应用",
      description: "重新启动HOUT应用程序",
      icon: <ArrowExit24Regular />,
      isRisky: true, // 设置为有风险操作，需要确认
      action: async () => {
        try {
          addNotification({
            type: "info",
            title: "正在重启",
            message: "应用将在3秒后重启...",
            duration: 3000,
          });
          // 延迟3秒后重启应用
          setTimeout(async () => {
            try {
              // 使用window.location.reload()作为备用方案
              window.location.reload();
            } catch (_relaunchError) {
              addNotification({
                type: "error",
                title: "重启失败",
                message: "无法重启应用，请手动重启",
              });
            }
          }, 3000);
        } catch (_error) {
          addNotification({
            type: "error",
            title: "重启失败",
            message: "无法重启应用，请手动重启",
          });
        }
      },
    },
  ];

  const handleFunctionClick = async (func: MiscFunction) => {
    if (executingFunction || func.isDisabled) return; // 添加禁用状态检查

    // 如果是有风险的操作且需要确认
    if (func.isRisky) {
      // 如果当前有待确认的操作且是同一个操作，执行操作
      if (pendingConfirmation && pendingConfirmation === func.id) {
        await executeFunction(func);
        return;
      }

      // 第一次点击：设置待确认状态
      setPendingConfirmation(func.id);

      // 清除之前的超时
      if (confirmationTimeout) {
        clearTimeout(confirmationTimeout);
      }

      // 设置5秒后自动清除确认状态
      const timeout = setTimeout(() => {
        setPendingConfirmation(null);
        setConfirmationTimeout(null);
      }, 5000);

      setConfirmationTimeout(timeout);

      addNotification({
        type: "warning",
        title: "确认操作",
        message: `请再次点击"${func.title}"确认执行操作`,
        duration: 5000,
      });
    } else {
      // 非风险操作直接执行
      await executeFunction(func);
    }
  };

  const executeFunction = async (func: MiscFunction) => {
    setExecutingFunction(func.id);
    setPendingConfirmation(null);
    
    if (confirmationTimeout) {
      clearTimeout(confirmationTimeout);
      setConfirmationTimeout(null);
    }

    try {
      await func.action();
    } catch (error) {
      addNotification({
        type: "error",
        title: "操作失败",
        message: `${func.title}执行失败: ${error}`,
      });
    } finally {
      setExecutingFunction(null);
    }
  };

  const getItemClassName = (func: MiscFunction) => {
    let className = styles.functionItem;
    if (func.isDisabled) {
      className += ` ${styles.disabledItem}`;
    } else if (func.isRisky && func.id === "clear-auth" && !selectedDevice) {
      className += ` ${styles.warningItem}`;
    } else if (pendingConfirmation === func.id) {
      className += ` ${styles.pendingItem}`;
    }
    return className;
  };



  return (
    <Card className={styles.card}>
      <CardHeader className={styles.cardHeader}>
        <Text className={styles.cardTitle}>
          <Wrench24Regular className={styles.titleIcon} />
          杂项功能
        </Text>
      </CardHeader>
      
      <div className={styles.cardContent}>
        {miscFunctions.map((func) => (
          <div
            key={func.id}
            className={getItemClassName(func)}
            onClick={() => handleFunctionClick(func)}
          >
            <div className={styles.functionInfo}>
              <div className={styles.functionIcon}>
                {executingFunction === func.id ? (
                  <Spinner size="tiny" />
                ) : pendingConfirmation === func.id ? (
                  <Warning24Regular />
                ) : (
                  func.icon
                )}
              </div>
              <div className={styles.functionText}>
                <Text className={styles.functionTitle}>{func.title}</Text>
              </div>
              {pendingConfirmation === func.id && (
                <Badge appearance="filled" color="warning" size="small">
                  待确认
                </Badge>
              )}
              {func.isRisky && pendingConfirmation !== func.id && (
                <Badge appearance="outline" color="warning" size="small">
                  风险
                </Badge>
              )}
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default MiscellaneousCard;
